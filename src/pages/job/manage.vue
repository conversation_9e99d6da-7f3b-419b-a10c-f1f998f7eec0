<template>
  <view class="manage-container">
    <!-- 顶部发布按钮 -->
    <view class="publish-btn-wrapper px-20rpx my-30rpx">
      <view class="publish-btn" @tap="goToPublish">
        <text class="i-carbon-add mr-10rpx"></text>
        <text>发布新职位</text>
      </view>
    </view>

    <!-- 数据概览 -->
    <view class="statistics-card mx-20rpx mb-30rpx">
      <view class="card-title mb-30rpx">
        <text class="i-carbon-chart-line mr-10rpx"></text>
        <text>数据概览</text>
      </view>
      <view class="grid grid-cols-3 gap-20rpx">
        <view class="stat-item">
          <text class="stat-value">{{ statistics.totalJobs }}</text>
          <text class="stat-label">在招职位</text>
        </view>
        <view class="stat-item">
          <text class="stat-value stat-views">{{ statistics.totalViews }}</text>
          <text class="stat-label">今日浏览</text>
        </view>
        <view class="stat-item">
          <text class="stat-value stat-resumes">{{
            statistics.totalResumes
          }}</text>
          <text class="stat-label">待处理简历</text>
        </view>
      </view>
    </view>
    <!-- 职位筛选 -->
    <view class="card-header mx-20rpx">
      <view class="tabs-container">
        <tui-segmented-control
          :values="tabs"
          :current="currentTab"
          height="64rpx"
          @click="handleTabClick"
          activeColor="#5677fc"
        />
      </view>
    </view>
    <!-- 职位列表 -->
    <view class="mx-20rpx mb-30rpx">
      <view v-if="filteredJobList.length > 0" class="job-list">
        <view
          v-for="(job, index) in filteredJobList"
          :key="job.id"
          class="job-item"
        >
          <view class="job-item-header">
            <view class="job-title-wrapper">
              <view class="job-title-row">
                <view class="job-title">{{ job.title }}</view>
                <view v-if="job.isUrgent" class="tag tag-sm urgent-tag"
                  >急聘</view
                >
                <view v-if="job.isTop" class="tag tag-sm top-tag">置顶</view>
              </view>
              <view class="job-salary">{{ job.salary }}</view>
            </view>
            <view class="job-status" :class="getStatusClass(job.status)">
              {{ job.statusText }}
            </view>
          </view>

          <view class="job-stats">
            <view class="job-stat-item">
              <text class="i-carbon-view"></text>
              <text>{{ job.views }}</text>
            </view>
            <view class="job-stat-item">
              <text class="i-carbon-user-profile"></text>
              <text>{{ job.applications }}</text>
            </view>
            <view class="job-stat-item">
              <text class="i-carbon-time"></text>
              <text>{{ job.remainingDays }}天后到期</text>
            </view>
          </view>

          <view class="job-actions">
            <view class="action-btn refresh-btn" @tap="refreshJob(job.id)">
              <text class="i-carbon-restart"></text>
              <text>刷新</text>
            </view>
            <view class="action-btn edit-btn" @tap="editJob(job.id)">
              <text class="i-carbon-edit"></text>
              <text>编辑</text>
            </view>
            <view
              class="action-btn status-btn"
              @tap="closeJob(job.id)"
              v-if="job.status === 'active'"
            >
              <text class="i-carbon-pause"></text>
              <text>下线</text>
            </view>
            <view
              class="action-btn status-btn"
              @tap="reopenJob(job.id)"
              v-else-if="job.status === 'closed'"
            >
              <text class="i-carbon-play"></text>
              <text>上线</text>
            </view>
            <view
              class="action-btn primary-btn"
              @tap="viewResumes(job.id)"
              v-if="job.applications > 0"
            >
              <text class="i-carbon-user-multiple"></text>
              <text>查看简历</text>
            </view>
            <view class="action-btn primary-btn" @tap="boostJob(job.id)" v-else>
              <text class="i-carbon-rocket"></text>
              <text>推广</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <text class="i-carbon-document-blank empty-icon"></text>
        <view class="empty-text">暂无发布的职位</view>
        <view class="empty-btn" @tap="goToPublish">立即发布</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 跳转到发布职位页面
const goToPublish = () => {
  uni.navigateTo({
    url: "/pages/job/publish",
  });
};

// 统计数据
const statistics = ref({
  totalJobs: 3,
  totalViews: 68,
  totalResumes: 12,
});

// 职位列表数据
const jobList = ref([
  {
    id: "001",
    title: "前端开发工程师",
    salary: "15K-25K",
    status: "active",
    statusText: "招聘中",
    isUrgent: true,
    isTop: true,
    views: 253,
    applications: 8,
    remainingDays: 12,
  },
  {
    id: "002",
    title: "销售经理",
    salary: "8K-15K",
    status: "active",
    statusText: "招聘中",
    isUrgent: false,
    isTop: false,
    views: 156,
    applications: 4,
    remainingDays: 5,
  },
  {
    id: "003",
    title: "人事专员",
    salary: "6K-8K",
    status: "closed",
    statusText: "已暂停",
    isUrgent: false,
    isTop: false,
    views: 86,
    applications: 0,
    remainingDays: 20,
  },
]);

// 筛选功能
const tabs = ref(["全部", "招聘中", "已暂停"]);
const currentTab = ref(0);

const handleTabClick = (e: any) => {
  if (e.index !== undefined) {
    currentTab.value = e.index;
  }
};

const filteredJobList = computed(() => {
  const tab = tabs.value[currentTab.value];
  if (tab === "招聘中") {
    return jobList.value.filter((job) => job.status === "active");
  }
  if (tab === "已暂停") {
    return jobList.value.filter((job) => job.status === "closed");
  }
  return jobList.value; // "全部"
});

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case "active":
      return "status-active";
    case "closed":
      return "status-closed";
    case "expired":
      return "status-expired";
    default:
      return "";
  }
};

// 刷新职位
const refreshJob = (jobId: string) => {
  uni.showToast({
    title: "刷新成功",
    icon: "success",
  });
};

// 编辑职位
const editJob = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/publish?id=${jobId}&edit=true`,
  });
};

// 下线职位
const closeJob = (jobId: string) => {
  uni.showModal({
    title: "提示",
    content: "确定要暂停此职位的招聘吗？",
    success: function (res) {
      if (res.confirm) {
        const index = jobList.value.findIndex((job) => job.id === jobId);
        if (index !== -1) {
          jobList.value[index].status = "closed";
          jobList.value[index].statusText = "已暂停";

          uni.showToast({
            title: "已暂停招聘",
            icon: "success",
          });
        }
      }
    },
  });
};

// 重新上线职位
const reopenJob = (jobId: string) => {
  const index = jobList.value.findIndex((job) => job.id === jobId);
  if (index !== -1) {
    jobList.value[index].status = "active";
    jobList.value[index].statusText = "招聘中";

    uni.showToast({
      title: "已恢复招聘",
      icon: "success",
    });
  }
};

// 查看简历
const viewResumes = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/resumes?jobId=${jobId}`,
  });
};

// 推广职位
const boostJob = (jobId: string) => {
  uni.navigateTo({
    url: `/pages/job/boost?jobId=${jobId}`,
  });
};
</script>

<style lang="scss" scoped>
.manage-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 导航栏样式
.nav-bar-wrapper {
  background: linear-gradient(
    to right,
    rgba($primary, 0.05),
    rgba($primary, 0.1)
  );
  margin-bottom: 10rpx;

  ::v-deep .uni-nav-bar-text {
    font-weight: 600 !important;
  }
}

// 发布按钮样式
.publish-btn-wrapper {
  padding-top: 10rpx;
}

.publish-btn {
  background: linear-gradient(to right, $primary, $primary-500);
  color: #fff;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba($primary, 0.3);
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);
  }
}

// 卡片通用样式
.statistics-card,
.job-list-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

// 卡片标题样式
.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: $text-base;
  display: flex;
  align-items: center;
}

// 卡片头部样式
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.job-count {
  font-size: 26rpx;
  color: $primary;
  font-weight: 500;
  flex-shrink: 0;
  margin-left: 20rpx;
}

.tabs-container {
  width: 100%;
}

// 统计卡片样式
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f9f9f9;
  transition: all 0.2s;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }
}

.stat-value {
  font-size: 40rpx;
  font-weight: 700;
  color: $primary;
  margin-bottom: 10rpx;
}

.stat-views {
  color: #3b82f6; // 蓝色
}

.stat-resumes {
  color: #10b981; // 绿色
}

.stat-label {
  font-size: 26rpx;
  color: $text-info;
}

// 职位列表样式
.job-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.job-item {
  padding: 30rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s;

  &:active {
    background-color: #fafafa;
  }
}

.job-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.job-title-wrapper {
  flex: 1;
}

.job-title-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.job-title {
  font-size: 36rpx;
  font-weight: 600;
  color: $text-base;
  margin-right: 10rpx;
  max-width: 400rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.job-salary {
  font-size: 34rpx;
  color: $primary;
  font-weight: 500;
  margin-top: 10rpx;
}

.urgent-tag {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
}

.top-tag {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1rpx solid rgba(245, 158, 11, 0.2);
}

// 职位状态样式
.job-status {
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1rpx solid rgba(16, 185, 129, 0.2);
}

.status-closed {
  background-color: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1rpx solid rgba(107, 114, 128, 0.2);
}

.status-expired {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
}

// 职位统计信息样式
.job-stats {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.job-stat-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 26rpx;
  color: $text-grey;

  text:first-child {
    margin-right: 6rpx;
  }
}

// 操作按钮样式
.job-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.2s;

  text:first-child {
    margin-right: 6rpx;
  }

  &:active {
    transform: scale(0.98);
  }
}

.refresh-btn,
.edit-btn,
.status-btn {
  border: 1rpx solid #e0e0e0;
  color: $text-info;
  background-color: #f9f9f9;

  &:active {
    background-color: #f0f0f0;
  }
}

.primary-btn {
  background-color: rgba($primary, 0.1);
  color: $primary;
  border: 1rpx solid rgba($primary, 0.2);
  font-weight: 500;

  &:active {
    background-color: rgba($primary, 0.15);
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  min-height: 300rpx;
}

.empty-icon {
  font-size: 80rpx;
  color: $text-grey;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: $text-grey;
  margin-bottom: 30rpx;
}

.empty-btn {
  display: inline-block;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #fff;
  background: linear-gradient(to right, $primary, $primary-500);
  box-shadow: 0 4rpx 12rpx rgba($primary, 0.3);
  transition: all 0.2s;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba($primary, 0.2);
  }
}
</style>
